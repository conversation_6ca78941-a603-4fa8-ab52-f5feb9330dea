import { useEffect, useState } from 'react';
import { Disclosure } from '@headlessui/react';
import DButtonIcon from '../DButtonIcon';
import DInputBlock from '../DInput/DInputBlock';
import DTransition from '../DTransition';
import DeleteIcon from '../Icons/DeleteIcon';
import DragIcon from '../Icons/DragIcon';
import DInput from '../DInput/DInput';
import DTextArea from '../DInput/DTextArea';
import DCheckbox from '../DCheckbox';
import DContentUpload from '../DContentUpload';
import * as tabsService from '@/services/tabs.service';
import DSpinner from '../DSpinner';
import DSwitch from '../DSwitch';
import ChevronDownIcon from '../Icons/ChevronDownIcon';

const SliderInput = ({
  item,
  onEdit,
  onDelete,
  className,
  error,
  dragHandleProps,
}) => {
  const [newData, setNewData] = useState(item);
  const [isLoading, setIsLoading] = useState(false);
  const [thumbnailFile, setThumbnailFile] = useState(null);

  // Decide if "pullMetaData" switch should be initially ON or OFF
  const [pullMetaData, setPullMetaData] = useState(
    Boolean(newData?.description || newData?.thumbnail_url)
  );

  /**
   * Whenever user changes a field, update local state and immediately inform
   * the parent so the parent can keep the "source of truth" in sync.
   */
  const handleChange = (field, value) => {
    const updatedItem = { ...newData, [field]: value };
    setNewData(updatedItem);
    onEdit?.(item?.id, updatedItem);
  };

  /**
   * Handle file upload for thumbnail
   */
  const handleThumbnailFileChange = (file) => {
    setThumbnailFile(file);
    if (file) {
      const updatedItem = {
        ...newData,
        thumbnail_url: URL.createObjectURL(file),
        thumbnail_file: file
      };
      setNewData(updatedItem);
      onEdit?.(item?.id, updatedItem);
    }
  };

  /**
   * "Pull meta data" logic - calls external service and updates 
   * thumbnail/description if successful.
   */
  const handlePullMetaDataChange = async () => {
    try {
      setIsLoading(true);
      const response = await tabsService.getMetaData(newData?.url);
      if (response.status === 200) {
        const { description, image_url } = response.data || {};
        const updatedItem = {
          ...newData,
          description: description ?? newData.description,
          thumbnail_url: image_url ?? newData.thumbnail_url,
        };
        setNewData(updatedItem);
        onEdit?.(item?.id, updatedItem);
      }
    } catch (err) {
      console.error('Error updating link:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Disclosure as="div" className={`p-size2 pb-0 ${className || ''}`}>
      {({ open }) => (
        <>
          {/* 
            The "header" of the accordion, which shows newData.name and 
            a delete button on the right. 
          */}
          <Disclosure.Button
            className="w-full text-left flex justify-between items-center gap-size1"
          >
            <div className="flex items-center gap-size1">
              <div {...dragHandleProps}>
                <DragIcon />
              </div>
              <p className="text-sm font-regular tracking-tight">
                {newData?.name || 'New Link'}
              </p>
            </div>

            <div className="flex items-center gap-size2">
              {/* Chevron Icon with rotation on open */}
              <ChevronDownIcon
                className={`transition-transform duration-200 size-3 ${
                  open ? 'rotate-180' : 'rotate-0'
                }`}
              />
              <DButtonIcon
                variant="outlined"
                onClick={(e) => {
                  e.stopPropagation(); // prevent toggling the accordion
                  onDelete?.(item?.id);
                }}
              >
                <DeleteIcon />
              </DButtonIcon>
            </div>
          </Disclosure.Button>

          <Disclosure.Panel className="pt-size4 bg-grey-5 px-size0 mt-size1">
            <div className={`flex flex-col gap-size2 ${error?.items ? 'pb-size4' : ''}`}>
              <DInputBlock label="Link title">
                <DInput
                  value={newData?.name ?? ''}
                  onChange={(e) => handleChange('name', e.target.value)}
                  error={error?.title}
                />
              </DInputBlock>

              <DInputBlock label="Link URL">
                <DInput
                  value={newData?.url ?? ''}
                  onChange={(e) => handleChange('url', e.target.value)}
                  error={error?.url}
                />
              </DInputBlock>

              <DCheckbox
                label="Open in new tab"
                checked={newData?.open_in_new_tab ?? false}
                onChange={(checked) => handleChange('open_in_new_tab', checked)}
                error={error?.open_in_new_tab}
              />

              <div className="flex items-center gap-size1 h-[30px]">
                <DSwitch
                  label="Pull meta data"
                  checked={pullMetaData}
                  onChange={() => {
                    const newValue = !pullMetaData;
                    setPullMetaData(newValue);
                    if (newValue) {
                      // Only fetch meta data when toggled ON
                      handlePullMetaDataChange();
                    }
                  }}
                />
                {isLoading && <DSpinner />}
              </div>

              {pullMetaData && (
                <>
                  <DInputBlock label="Description">
                    <DTextArea
                      value={newData?.description ?? ''}
                      onChange={(e) => handleChange('description', e.target.value)}
                      error={error?.description}
                    />
                  </DInputBlock>

                  <DContentUpload
                    label="Image"
                    urlPlaceholder="Enter image URL"
                    urlValue={newData?.thumbnail_url ?? ''}
                    onUrlChange={(e) => {
                      handleChange('thumbnail_url', e.target.value);
                      setThumbnailFile(null); // Clear file when URL is entered
                    }}
                    onFileChange={handleThumbnailFileChange}
                    acceptedFileTypes="image/*"
                    maxFileSize="10MB"
                    supportedFormats="Images: PNG, JPEG, JPG, GIF, SVG"
                    error={error?.thumbnail_url}
                  />
                </>
              )}

              {error?.items && (
                <p className="font-regular tracking-tight text-error">
                  {error?.items}
                </p>
              )}
            </div>
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
};

export default SliderInput;
